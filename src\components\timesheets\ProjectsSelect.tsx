import { ComponentProps, useEffect, useMemo } from "react";
import { useInView } from "react-intersection-observer";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import useGetProjectsForTimesheet from "@/services/timesheet/getProjectsForTimesheet";
import { cn } from "@/lib/utils";
import { LoadingSpinner } from "../ui/loader";
import { useGetPaginatedProjects } from "../../services/timesheet/getPaginatedProjects";

type ProjectSelectProps = ComponentProps<typeof Select>;

const ProjectsSelect = ({ value, ...props }: ProjectSelectProps) => {
  // const { ref, inView } = useInView();

  // const { data, isPending, fetchNextPage, hasNextPage, isFetchingNextPage } =
  //   useGetProjectsForTimesheet({ search: "" });

  // const projects = useMemo(
  //   () => (data ? data.pages.flatMap((page) => page.data) : []),
  //   [data],
  // );

  // useEffect(() => {
  //   if (inView && hasNextPage && !isFetchingNextPage) {
  //     fetchNextPage();
  //   }
  // }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  const {
    data: projects,
    isPending,
    isFetchingNextPage,
    ref,
  } = useGetPaginatedProjects();

  return (
    <Select {...props}>
      <SelectTrigger
        loading={isPending}
        className="data-[placeholder]:text-name-title text-name-title font-medium"
      >
        <SelectValue placeholder="Select">
          {projects.find((project) => project._id === value)?.name}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {projects.length > 0 ? (
          <>
            {projects.map((project) => (
              <SelectItem key={project._id} value={project._id}>
                {project.name}
              </SelectItem>
            ))}
            <SelectItem
              ref={ref}
              value={"loading-spinner"}
              disabled
              className={cn(
                "data-[disabled]:opacity-100 justify-center ",
                isFetchingNextPage ? "mb-1" : "h-0",
              )}
            >
              {isFetchingNextPage && (
                <LoadingSpinner size={4} className="border-2 " />
              )}
            </SelectItem>
          </>
        ) : (
          <SelectItem value="no-projects" disabled>
            No projects found
          </SelectItem>
        )}
      </SelectContent>
    </Select>
  );
};

export default ProjectsSelect;
