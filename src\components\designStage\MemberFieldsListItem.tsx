import { UseFormReturn, useWatch } from "react-hook-form";
import { z } from "zod";
import { MouseEvent, useEffect, useMemo } from "react";
import { useInView } from "react-intersection-observer";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { LoadingSpinner } from "../ui/loader";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { designStageSchema } from "@/schema/designStage";
import useGetTeamMembers from "@/services/teamMember/getTeamMembers";
import { cn } from "@/lib/utils";
import { Input } from "../ui/input";
import MinusCircle from "../icons/MinusCircle";
import useGetDesignStageMembers from "@/services/designStage/getDesignStageMembers";

type MemberFieldsListItemProps = {
  form: UseFormReturn<z.infer<typeof designStageSchema>>;
  index: number;
  isFieldRemovable?: boolean;
  onRemoveClick?: (e: MouseEvent<HTMLButtonElement>) => void;
  projectId: string;
};

const MemberFieldsListItem = ({
  form,
  index,
  isFieldRemovable = false,
  onRemoveClick,
  projectId,
}: MemberFieldsListItemProps) => {
  // const { ref, inView } = useInView();

  // const { data, isPending, fetchNextPage, hasNextPage, isFetchingNextPage } =
  //   useGetTeamMembers({ search: "" });

  const { data, isPending: isPendingStageMembers } =
    useGetDesignStageMembers(projectId);

  // const teamMembers = useMemo(
  //   () => (data ? data.pages.flatMap((page) => page.data) : []),
  //   [data],
  // );

  const memberId = useWatch({
    control: form.control,
    name: `members.${index}.memberId`,
  });

  useEffect(() => {
    if (!memberId || !data?.teamMembers) return;

    const member = data?.teamMembers?.find(
      (member: any) => member.userId === memberId,
    );
    if (member) {
      form.setValue(`members.${index}.role`, member.role);
      form.setValue(`members.${index}.name`, member.name);
    }
  }, [memberId, data?.teamMembers, form, index]);

  // useEffect(() => {
  //   if (inView && hasNextPage && !isFetchingNextPage) {
  //     fetchNextPage();
  //   }
  // }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  return (
    <div className="space-y-2">
      <div className="flex gap-x-2">
        <FormField
          control={form.control}
          name={`members.${index}.memberId`}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormControl>
                <>
                  <FormLabel className="inline-flex gap-x-1 items-center">
                    Assign member{" "}
                    <button
                      type="button"
                      onClick={onRemoveClick || undefined}
                      className={!isFieldRemovable ? "hidden" : ""}
                    >
                      <MinusCircle />
                    </button>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value || ""}
                  >
                    <FormControl>
                      <SelectTrigger
                        loading={isPendingStageMembers}
                        className="data-[placeholder]:text-name-title text-name-title font-medium"
                      >
                        <SelectValue placeholder="Select">
                          {
                            data?.teamMembers?.find(
                              (member: any) => member.userId === field.value,
                            )?.name
                          }
                        </SelectValue>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {data?.teamMembers.length > 0 ? (
                        <>
                          {data?.teamMembers?.map((member: any) => (
                            <SelectItem
                              key={member.userId}
                              value={member.userId}
                            >
                              {member.name}
                            </SelectItem>
                          ))}
                          {/* <SelectItem
                            ref={ref}
                            value={"none"}
                            disabled
                            className={cn(
                              "data-[disabled]:opacity-100 justify-center ",
                              isFetchingNextPage ? "mb-1" : "h-0",
                            )}
                          >
                            {isFetchingNextPage && (
                              <LoadingSpinner size={4} className="border-2 " />
                            )}
                          </SelectItem> */}
                        </>
                      ) : (
                        <SelectItem value="no-team-members" disabled>
                          No team members found
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* <FormField
          control={form.control}
          name={`members.${index}.hours`}
          render={({ field }) => (
            <FormItem className="basis-[65px]">
              <FormLabel>Hours</FormLabel>
              <FormControl>
                <Input type="number" placeholder="00" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        /> */}
      </div>
      <FormField
        control={form.control}
        name={`members.${index}.role`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Role</FormLabel>
            <FormControl>
              <Input
                disabled
                placeholder="Enter"
                {...field}
                className="bg-neutrals-G40"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default MemberFieldsListItem;
