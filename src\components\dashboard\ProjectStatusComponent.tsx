"use client";

import { useState } from "react";
import Image from "next/image";

import { ProjectData } from "@/types/Project";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import CompletionDialog from "./CompletionDialog";
import ContractorRatingDialog from "./ContractorRatingDialog";

type ProjectStatusComponentProps = {
  projectData: ProjectData;
};

type PhaseStatus = "ongoing" | "halted" | "completed";

const ProjectStatusComponent: React.FC<ProjectStatusComponentProps> = ({
  projectData,
}) => {
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [designStatus, setDesignStatus] = useState<PhaseStatus>("ongoing");
  const [constructionStatus, setConstructionStatus] =
    useState<PhaseStatus>("ongoing");
  const [completionDialogOpen, setCompletionDialogOpen] = useState(false);
  const [contractorRatingDialogOpen, setContractorRatingDialogOpen] =
    useState(false);
  const [completionType, setCompletionType] = useState<
    "design" | "construction" | "both" | null
  >(null);

  // Determine which phases are active based on project scope
  const projectScope = projectData.projectScope;
  const hasDesign = projectScope === "design" || projectScope === "both";
  const hasConstruction =
    projectScope === "construction" || projectScope === "both";

  const handleStatusChange = (
    phase: "design" | "construction",
    newStatus: PhaseStatus,
  ) => {
    if (phase === "design") {
      setDesignStatus(newStatus);
    } else {
      setConstructionStatus(newStatus);
    }
  };

  const handleSaveChanges = () => {
    // Check if any status is completed to show completion dialog
    const isDesignCompleted = hasDesign && designStatus === "completed";
    const isConstructionCompleted =
      hasConstruction && constructionStatus === "completed";

    if (isDesignCompleted && isConstructionCompleted) {
      setCompletionType("both");
    } else if (isDesignCompleted) {
      setCompletionType("design");
    } else if (isConstructionCompleted) {
      setCompletionType("construction");
    }

    // If any completion, show completion dialog
    if (isDesignCompleted || isConstructionCompleted) {
      setCompletionDialogOpen(true);
    } else {
      // No completion, just save and close
      console.log("Saving status changes:", {
        designStatus,
        constructionStatus,
      });
      setStatusDialogOpen(false);
    }
  };

  const handleCompletionConfirm = () => {
    // Check if construction is completed to show contractor rating
    const isConstructionCompleted =
      hasConstruction && constructionStatus === "completed";

    if (isConstructionCompleted) {
      // Close completion dialog and show contractor rating
      setCompletionDialogOpen(false);
      setContractorRatingDialogOpen(true);
    } else {
      // No contractor rating needed, just complete
      handleFinalCompletion();
    }
  };

  const handleContractorRatingComplete = () => {
    setContractorRatingDialogOpen(false);
    handleFinalCompletion();
  };

  const handleContractorRatingGoBack = () => {
    setContractorRatingDialogOpen(false);
    setCompletionDialogOpen(true);
  };

  const handleFinalCompletion = () => {
    // TODO: Implement final API call to save completion status
    console.log("Final completion:", {
      designStatus,
      constructionStatus,
      completionType,
    });
    setStatusDialogOpen(false);
    setCompletionDialogOpen(false);
    setContractorRatingDialogOpen(false);
  };

  const getStatusColor = (status: PhaseStatus) => {
    switch (status) {
      case "ongoing":
        return "text-ongoing";
      case "completed":
        return "text-completed";
      case "halted":
        return "text-halted";
      default:
        return "text-gray-700";
    }
  };

  const getStatusDot = (status: PhaseStatus) => {
    switch (status) {
      case "ongoing":
        return "bg-ongoing";
      case "completed":
        return "bg-completed";
      case "halted":
        return "bg-halted";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <>
      <div
        className="flex items-center gap-4 cursor-pointer border py-3 px-4 rounded-xl bg-[#F7FAFE]"
        onClick={() => setStatusDialogOpen(true)}
      >
        {hasDesign && (
          <div className="flex items-center gap-2">
            <Image
              src="/design.png"
              alt="Design"
              width={20}
              height={20}
              className="object-contain"
            />
            <span className="text-sm">Design status:</span>

            <div className="flex items-center gap-1">
              <div
                className={`w-2 h-2 rounded-full ${getStatusDot(designStatus)}`}
              />
              <span
                className={`text-sm font-medium ${getStatusColor(designStatus)}`}
              >
                {designStatus}
              </span>
            </div>
          </div>
        )}
        <div className="bg-primary-blue-B50 w-[1px] h-[22px]"></div>
        {hasConstruction && (
          <div className="flex items-center gap-2">
            <Image
              src="/construction.png"
              alt="Construction"
              width={20}
              height={20}
              className="object-contain"
            />
            <div className="flex items-center gap-1">
              <span className="text-sm">Construction status:</span>

              <div
                className={`w-2 h-2 rounded-full ${getStatusDot(constructionStatus)}`}
              />
              <span
                className={`text-sm font-medium ${getStatusColor(constructionStatus)}`}
              >
                {constructionStatus}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Status Update Dialog */}
      <Dialog open={statusDialogOpen} onOpenChange={setStatusDialogOpen}>
        <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
          <DialogHeader>
            <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
              Update Project Status
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 mt-4">
            {hasDesign && (
              <div className="flex items-center justify-between gap-4">
                <label className="text-sm font-medium text-[#1E1E1E] whitespace-nowrap">
                  Design Phase Status:
                </label>
                <Select
                  value={designStatus}
                  onValueChange={(value: PhaseStatus) =>
                    handleStatusChange("design", value)
                  }
                >
                  <SelectTrigger className="w-[200px]">
                    <div className="flex items-center gap-2">
                      <SelectValue />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ongoing">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-ongoing" />
                        <span className="text-ongoing text-sm font-medium">
                          Ongoing
                        </span>
                      </div>
                    </SelectItem>
                    <SelectItem value="halted">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-halted" />
                        <span className="text-halted text-sm font-medium">
                          Halted
                        </span>{" "}
                      </div>
                    </SelectItem>
                    <SelectItem value="completed">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-completed" />
                        <span className="text-completed text-sm font-medium">
                          Completed
                        </span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {hasConstruction && (
              <div className="flex items-center justify-between gap-4">
                <label className="text-sm font-medium text-[#1E1E1E] whitespace-nowrap">
                  Construction Phase Status:
                </label>
                <Select
                  value={constructionStatus}
                  onValueChange={(value: PhaseStatus) =>
                    handleStatusChange("construction", value)
                  }
                >
                  <SelectTrigger className="w-[200px]">
                    <div className="flex items-center gap-2">
                      <SelectValue />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ongoing">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-ongoing" />
                        <span className="text-ongoing text-sm font-medium">
                          Ongoing
                        </span>
                      </div>
                    </SelectItem>
                    <SelectItem value="halted">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-halted" />
                        <span className="text-halted text-sm font-medium">
                          Halted
                        </span>
                      </div>
                    </SelectItem>
                    <SelectItem value="completed">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-completed" />
                        <span className="text-completed text-sm font-medium">
                          Completed
                        </span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setStatusDialogOpen(false)}
              className="px-4"
            >
              Cancel
            </Button>
            <Button onClick={handleSaveChanges} className="px-4">
              Save changes
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Completion Dialog */}
      <CompletionDialog
        open={completionDialogOpen}
        onOpenChange={setCompletionDialogOpen}
        completionType={completionType}
        onConfirm={handleCompletionConfirm}
        hasContractorRating={
          hasConstruction && constructionStatus === "completed"
        }
      />

      {/* Contractor Rating Dialog */}
      <ContractorRatingDialog
        open={contractorRatingDialogOpen}
        onOpenChange={setContractorRatingDialogOpen}
        onGoBack={handleContractorRatingGoBack}
        onComplete={handleContractorRatingComplete}
        contractorName={projectData.contractorOrg?.name}
        contractorLocation="Kochi, India" // TODO: Get from contractor data
      />
    </>
  );
};

export default ProjectStatusComponent;
