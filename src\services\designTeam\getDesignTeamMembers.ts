import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { DesignTeamMember } from "@/types/DesignTeamMember";

export type GetDesignTeamMembers = {
  projectId: string;
  search?: string;
};

const getDesignTeamMembers = async ({
  projectId,
  search,
}: GetDesignTeamMembers): Promise<{
  teamMembers: DesignTeamMember[];
  teamMembersCount: number;
}> => {
  const response = await api.get(
    `/design-team/get-design-team/${projectId}?search=${search}`,
  );
  return response.data;
};

type UseGetDesignTeamMembers = GetDesignTeamMembers;

const useGetDesignTeamMembers = ({
  projectId,
  search,
}: UseGetDesignTeamMembers) => {
  return useQuery({
    queryKey: ["design-team", projectId, search],
    queryFn: async () => getDesignTeamMembers({ projectId, search }),
    enabled: !!projectId,
  });
};

export default useGetDesignTeamMembers;
