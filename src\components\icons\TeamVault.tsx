import React from "react";

const TeamVault: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    width="18"
    height="15"
    viewBox="0 0 18 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.9375 0.125C13.5369 0.125414 14.1204 0.317294 14.6031 0.672659C15.0857 1.02802 15.4422 1.52829 15.6206 2.1005L17.6006 9.008C17.7011 9.329 17.7806 9.65375 17.8391 9.98225C17.9194 10.2463 17.9602 10.523 17.9617 10.8125V11.9375C17.9617 12.6834 17.6659 13.4 17.1382 13.9288C16.6106 14.4564 15.9007 14.7523 15.147 14.7523H2.772C2.02612 14.7523 1.3095 14.4564 0.780746 13.9288C0.51933 13.6673 0.312038 13.3568 0.170733 13.0151C0.0294276 12.6734 -0.0431183 12.3072 -0.0427538 11.9375V10.8125C-0.0427538 10.5313 -0.00112876 10.2511 0.0809962 9.98225C0.139344 9.65279 0.21863 9.32738 0.318371 9.008L2.29837 2.1005C2.47674 1.52829 2.83325 1.02802 3.31591 0.672659C3.79857 0.317294 4.38213 0.125414 4.9815 0.125H12.924H12.9375ZM15.1537 9.125H2.77875C2.42749 9.12409 2.08477 9.23319 1.79867 9.43697C1.51257 9.64075 1.29745 9.92899 1.1835 10.2613C1.12201 10.6293 1.09116 11.0018 1.09125 11.375V11.9375C1.09154 12.3847 1.26931 12.8134 1.5855 13.1296C1.9017 13.4458 2.33046 13.6236 2.77762 13.6239H15.1526C15.5998 13.6236 16.0285 13.4458 16.3447 13.1296C16.6609 12.8134 16.8387 12.3847 16.839 11.9375V11.375C16.839 11.0008 16.8086 10.6295 16.7479 10.2613C16.6331 9.92927 16.4177 9.64137 16.1315 9.43769C15.8453 9.23401 15.5027 9.1247 15.1515 9.125H15.1537ZM14.5912 10.25C14.7391 10.2496 14.8856 10.2783 15.0223 10.3347C15.159 10.3911 15.2832 10.474 15.3877 10.5785C15.4923 10.6831 15.5751 10.8073 15.6315 10.944C15.6879 11.0806 15.7167 11.2271 15.7162 11.375C15.7167 11.5229 15.6879 11.6694 15.6315 11.806C15.5751 11.9427 15.4923 12.0669 15.3877 12.1715C15.2832 12.276 15.159 12.3589 15.0223 12.4153C14.8856 12.4717 14.7391 12.5004 14.5912 12.5C14.4434 12.5004 14.2969 12.4717 14.1602 12.4153C14.0235 12.3589 13.8993 12.276 13.7948 12.1715C13.6902 12.0669 13.6074 11.9427 13.551 11.806C13.4946 11.6694 13.4658 11.5229 13.4662 11.375C13.4658 11.2271 13.4946 11.0806 13.551 10.944C13.6074 10.8073 13.6902 10.6831 13.7948 10.5785C13.8993 10.474 14.0235 10.3911 14.1602 10.3347C14.2969 10.2783 14.4434 10.2496 14.5912 10.25ZM12.9375 1.25H4.995C4.63565 1.25044 4.28583 1.36558 3.99647 1.57866C3.70712 1.79173 3.49333 2.09161 3.38625 2.43463L1.76625 8.19463C2.08125 8.07088 2.42325 8.0045 2.78325 8.0045H15.1582C15.5171 8.0045 15.8602 8.072 16.1752 8.19463L14.5552 2.43463C14.4489 2.09096 14.2351 1.79044 13.9454 1.57723C13.6556 1.36401 13.3051 1.24932 12.9454 1.25H12.9375Z"
      fill="currentColor"
    />
  </svg>
);

export default TeamVault;
