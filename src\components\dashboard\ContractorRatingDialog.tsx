"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ArrowLeft, X, MapPin } from "lucide-react";

type ContractorRatingDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGoBack: () => void;
  onComplete: () => void;
  contractorName?: string;
  contractorLocation?: string;
};

const ContractorRatingDialog: React.FC<ContractorRatingDialogProps> = ({
  open,
  onOpenChange,
  onGoBack,
  onComplete,
  contractorName = "Ra<PERSON> Sharma",
  contractorLocation = "Kochi, India",
}) => {
  const [rating, setRating] = useState<number>(0);
  const [hoveredRating, setHoveredRating] = useState<number>(0);

  const handleStarClick = (starIndex: number) => {
    setRating(starIndex);
  };

  const handleStarHover = (starIndex: number) => {
    setHoveredRating(starIndex);
  };

  const handleStarLeave = () => {
    setHoveredRating(0);
  };

  const handleSubmit = () => {
    // TODO: Implement API call to submit rating
    console.log("Submitting contractor rating:", { rating, contractorName });
    onComplete();
  };

  const handleSkip = () => {
    console.log("Skipping contractor rating");
    onComplete();
  };

  const renderStars = () => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      const isFilled = i <= (hoveredRating || rating);
      stars.push(
        <button
          key={i}
          onClick={() => handleStarClick(i)}
          onMouseEnter={() => handleStarHover(i)}
          onMouseLeave={handleStarLeave}
          className="focus:outline-none transition-colors duration-200"
        >
          <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="transition-colors duration-200"
          >
            <path
              d="M16 2L20.12 10.24L29.64 11.64L22.82 18.32L24.24 27.76L16 23.44L7.76 27.76L9.18 18.32L2.36 11.64L11.88 10.24L16 2Z"
              fill={isFilled ? "#DA9500" : "none"}
              stroke={isFilled ? "#DA9500" : "#DA9500"}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>,
      );
    }
    return stars;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div className="flex items-center gap-3">
            <button
              onClick={onGoBack}
              className="flex items-center gap-2 text-sm text-[#474747] hover:text-[#1E1E1E] transition-colors"
            >
              <ArrowLeft size={16} />
              Go back
            </button>
          </div>
          <button
            onClick={() => onOpenChange(false)}
            className="text-[#474747] hover:text-[#1E1E1E] transition-colors"
          >
            <X size={20} />
          </button>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E] mb-2">
              Rate the Contractor
            </DialogTitle>
            <p className="text-[#474747] text-sm">
              Your feedback helps maintain quality standards and supports better
              collaboration in future projects.
            </p>
          </div>

          {/* Contractor Info */}
          <div className="bg-[#F6F6F6] rounded-lg p-4">
            <h3 className="font-semibold text-[#1E1E1E] text-lg mb-1">
              {contractorName}
            </h3>
            <div className="flex items-center gap-1 text-[#474747] text-sm">
              <MapPin size={14} />
              <span>{contractorLocation}</span>
            </div>
          </div>

          {/* Rating Section */}
          <div className="space-y-3">
            <label className="text-sm font-medium text-[#1E1E1E]">Rate</label>
            <div className="flex gap-2">{renderStars()}</div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={handleSkip}
              className="px-6 text-[#474747] border-[#E2E2E2]"
            >
              Skip
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={rating === 0}
              className="px-6 bg-[#2F80ED] hover:bg-[#2F80ED]/90"
            >
              Submit and complete
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ContractorRatingDialog;
