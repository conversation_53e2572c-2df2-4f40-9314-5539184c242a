"use client";

import { useState, useRef } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CirclePlus, Plus } from "lucide-react";
import { format, formatDate } from "date-fns";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  designStageSchema,
  createDesignStageSchemaWithProjectValidation,
} from "@/schema/designStage";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import CalendarIcon from "@/components/icons/Calendar";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import MemberFieldsListItem from "./MemberFieldsListItem";
import useAddDesignStage from "@/services/designStage/addDesignStage";
import Duration from "../milestones/formFields/Duration";
import { useSheetFormState } from "@/hooks/useSheetFormState";
import useGetProjectById from "@/services/project/getProject";

type AddStageProps = {
  projectId: string;
};

const AddStage = ({ projectId }: AddStageProps) => {
  const [open, setOpen] = useState(false);

  const popoverCloseRef = useRef<HTMLButtonElement>(null);

  // Fetch project data to get timeline for validation
  const { data: projectData } = useGetProjectById(projectId);

  // Create schema with project timeline validation
  const schemaWithProjectValidation =
    createDesignStageSchemaWithProjectValidation(
      projectData?.startDate ? new Date(projectData.startDate) : undefined,
      projectData?.endDate ? new Date(projectData.endDate) : undefined,
    );

  const form = useForm<z.infer<typeof designStageSchema>>({
    resolver: zodResolver(schemaWithProjectValidation),
    defaultValues: {
      projectId,
      stageName: "",
      members: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "members",
  });

  const { mutate, isPending } = useAddDesignStage(() => {
    setOpen(false);
    toast.success("Design stage added successfully!");
  });

  const { handleOpenChange, handleExplicitClose } = useSheetFormState({
    form,
    setOpen,
    preserveOnImplicitClose: true,
  });

  const onSubmit = async (values: z.infer<typeof designStageSchema>) => {
    // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    const { startDate, endDate, members, ...rest } = values;

    // Filter out empty members (members without memberId)
    const validMembers =
      members?.filter(
        (member) => member.memberId && member.memberId.trim() !== "",
      ) || [];

    mutate({
      // startDate: startDate.toISOString().split("T")[0],
      startDate: formatDate(values.startDate, "yyyy-MM-dd"),
      members: validMembers,
      ...rest,
    });
  };

  const handleAddMember = () => {
    append({
      memberId: "",
      name: "",
      role: "",
      hours: 0,
    });
  };

  // const handleRemoveMember = () => {
  //   remove(fields.length - 1);
  // };

  const handleRemoveMember = (index: number) => {
    remove(index);
  };

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        <Button className="gap-x-2 pl-4 pr-3">
          <Plus className="size-[22px] stroke-[2.5px]" />
          Add Stage
        </Button>
      </SheetTrigger>
      <SheetContent
        className="max-w-[507px] flex flex-col gap-0"
        onExplicitClose={handleExplicitClose}
      >
        <SheetHeader>
          <SheetTitle>Add a new stage</SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between flex-1"
          >
            <div className="space-y-4">
              <div className="flex gap-x-3">
                <FormField
                  control={form.control}
                  name="stageName"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel>Schedule name</FormLabel>
                      <FormControl>
                        <Input placeholder="Rough Design" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="budgetAllocated"
                  render={({ field }) => (
                    <FormItem className="basis-[128px]">
                      <FormLabel>Budget allocated</FormLabel>

                      <div className="relative ">
                        <FormControl>
                          <Input className="pr-[46px]" {...field} />
                        </FormControl>
                        <span className="absolute top-1/2 right-3 -translate-y-1/2 text-neutrals-G100 font-medium">
                          %
                        </span>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex gap-x-3">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem className="basis-1/2">
                      <FormLabel>Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="input"
                              className={cn(
                                "text-left",
                                !field.value && "text-neutrals-G100",
                              )}
                            >
                              {field.value ? (
                                format(field.value, "dd/MM/yyyy")
                              ) : (
                                <span>Choose date</span>
                              )}
                              <CalendarIcon className="ml-auto" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <PopoverClose
                            ref={popoverCloseRef}
                            className="hidden"
                          />
                          <Calendar
                            selected={field.value ?? undefined}
                            onSelect={(value) => {
                              popoverCloseRef.current?.click();
                              field.onChange(value);
                            }}
                            mode="single"
                            showOutsideDays={false}
                            // startMonth={new Date(Date.now())}
                            endMonth={
                              new Date(
                                Date.now() + 1000 * 60 * 60 * 24 * 365 * 10,
                              )
                            }
                          />
                        </PopoverContent>
                      </Popover>

                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem className="basis-1/2">
                      <FormLabel>End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="input"
                              className={cn(
                                "text-left",
                                !field.value && "text-neutrals-G100",
                              )}
                            >
                              {field.value ? (
                                format(field.value, "dd/MM/yyyy")
                              ) : (
                                <span>Choose date</span>
                              )}
                              <CalendarIcon className="ml-auto" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <PopoverClose
                            ref={popoverCloseRef}
                            className="hidden"
                          />
                          <Calendar
                            selected={field.value ?? undefined}
                            onSelect={(value) => {
                              popoverCloseRef.current?.click();
                              field.onChange(value);
                            }}
                            mode="single"
                            showOutsideDays={false}
                            endMonth={
                              new Date(
                                Date.now() + 1000 * 60 * 60 * 24 * 365 * 10,
                              )
                            }
                          />
                        </PopoverContent>
                      </Popover>

                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <Duration
                value={form.watch("duration") || 0}
                onChange={(val) => {
                  form.setValue("duration", val);
                }}
                startDate={form.watch("startDate")}
                endDate={form.watch("endDate")}
              />
              <div className="border border-neutrals-G40 p-4 rounded-[8px] space-y-4">
                {fields.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500 mb-4">
                      No team members assigned
                    </p>
                    <Button
                      onClick={handleAddMember}
                      type="button"
                      variant="link"
                      className="hover:no-underline font-medium gap-x-1"
                    >
                      <CirclePlus className="size-4" />
                      Add Member
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-10">
                    {fields.map((field, index) => (
                      <div key={field.id} className="flex flex-col">
                        <MemberFieldsListItem
                          form={form}
                          index={index}
                          isFieldRemovable={fields.length > 1}
                          onRemoveClick={() => handleRemoveMember(index)}
                          projectId={projectId}
                        />
                      </div>
                    ))}
                    <Button
                      onClick={handleAddMember}
                      type="button"
                      variant="link"
                      className="hover:no-underline font-medium gap-x-1 self-end"
                    >
                      <CirclePlus className="size-4" />
                      Add Member
                    </Button>
                  </div>
                )}
              </div>
            </div>
            <Button
              className="ml-auto px-4 py-3 sticky bottom-0"
              type="submit"
              loading={isPending}
            >
              Create Stage
            </Button>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};

export default AddStage;
