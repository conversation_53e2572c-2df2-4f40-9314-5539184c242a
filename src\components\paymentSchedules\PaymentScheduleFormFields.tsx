"use client";

import { paymentScheduleSchema } from "@/schema/paymentSchedule";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { useRef } from "react";
import { format } from "date-fns";

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "../ui/button";
import { cn } from "@/lib/utils";
import CalendarIcon from "@/components/icons/Calendar";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { LoadingSpinner } from "../ui/loader";
import useGetDesignStages from "@/services/designStage/getDesignStages";
import { useParams } from "next/navigation";

type PaymentScheduleFormFieldsProps = {
  form: UseFormReturn<z.infer<typeof paymentScheduleSchema>>;
  filteredStageId?: string;
};

const PaymentScheduleFormFields = ({
  form,
  filteredStageId,
}: PaymentScheduleFormFieldsProps) => {
  const popoverCloseRef = useRef<HTMLButtonElement>(null);
  const params = useParams();
  const projectId = params?.id as string;

  const { data: stages, isLoading } = useGetDesignStages({
    projectId: projectId,
    search: "",
  });

  return (
    <>
      <div className="flex gap-x-3">
        <FormField
          control={form.control}
          name="scheduleName"
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormLabel>Schedule name</FormLabel>
              <FormControl>
                <Input placeholder="Rough Design" {...field} required />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="input"
                      className={cn(
                        "text-left w-[118px] flex",
                        !field.value && "text-neutrals-G100",
                      )}
                    >
                      {field.value ? (
                        format(field.value, "dd/MM/yyyy")
                      ) : (
                        <span>Choose</span>
                      )}
                      <CalendarIcon className="ml-auto" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <PopoverClose ref={popoverCloseRef} className="hidden" />
                  <Calendar
                    selected={field.value ?? undefined}
                    onSelect={(value) => {
                      popoverCloseRef.current?.click();
                      field.onChange(value);
                    }}
                    mode="single"
                    showOutsideDays={false}
                    endMonth={
                      new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10)
                    }
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="stageId"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Design Stage</FormLabel>
            <Select onValueChange={field.onChange} value={field.value || ""}>
              <FormControl>
                <SelectTrigger className="w-full">
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <LoadingSpinner size={4} />
                      <span>Loading...</span>
                    </div>
                  ) : (
                    <SelectValue placeholder="Choose" />
                  )}
                </SelectTrigger>
              </FormControl>
              <SelectContent className="max-h-52 max-w-[--radix-select-trigger-width]">
                {stages && stages.length > 0 ? (
                  stages.map((stage) => (
                    <SelectItem key={stage._id} value={stage._id}>
                      {stage.stageName}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-stages" disabled>
                    No stages found
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default PaymentScheduleFormFields;
