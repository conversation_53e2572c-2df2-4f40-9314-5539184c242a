import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import api from "@/lib/api-client";

type RenameFolderParams = {
  folderId: string;
  newName: string;
};

const renameFolderInTeamVault = async ({
  folderId,
  newName,
}: RenameFolderParams) => {
  const { data } = await api({
    url: `/vault/folder/${folderId}/rename`,
    method: "PATCH",
    data: { newName },
  });
  return data;
};

const useRenameFolderInTeamVault = (onSuccess?: () => void) => {
  return useMutation({
    mutationFn: renameFolderInTeamVault,
    onSuccess: () => {
      onSuccess?.();
      toast.success("Folder renamed successfully");
    },
    onError: () => {
      toast.error("Failed to rename folder");
    },
  });
};

export default useRenameFolderInTeamVault;
