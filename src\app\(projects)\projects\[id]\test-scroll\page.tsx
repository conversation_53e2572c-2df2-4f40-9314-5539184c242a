"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";

const TestScrollPage = () => {
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);

  return (
    <div className="flex flex-col gap-y-3">
      <div className="flex justify-between items-center py-3.5 border-b border-neutrals-G40">
        <div className="space-y-1 text-neutrals-G900">
          <h4 className="font-semibold">Test Scroll Page</h4>
          <p className="text-xs text-neutrals-G300">
            Testing scroll behavior with similar content structure
          </p>
        </div>
        <Button disabled={isButtonDisabled} className="px-4 min-w-[124px]">
          Save changes
        </Button>
      </div>
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <form className="space-y-6">
          {/* Section 1 - Basic Details */}
          <div className="border border-border-gray1 rounded-xl">
            <h5 className="px-6 py-4 text-neutrals-G800 font-semibold border-b border-[#E6E6E6]">
              Basic details
            </h5>
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-2 gap-x-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Project Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                    placeholder="Enter project name"
                    defaultValue="Test Project Name"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Project Type
                  </label>
                  <select className="w-full px-3 py-2 border border-border-gray1 rounded-lg">
                    <option>Commercial</option>
                    <option>Residential</option>
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-x-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Number of Floors
                  </label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                    placeholder="Enter number of floors"
                    defaultValue="5"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Project Scope
                  </label>
                  <div className="flex gap-4 pt-2">
                    <label className="flex items-center gap-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Design</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Construction</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 2 - Design Section */}
          <div className="border border-border-gray1 rounded-xl">
            <h5 className="px-6 py-4 text-neutrals-G800 font-semibold border-b border-[#E6E6E6]">
              Design Section
            </h5>
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-2 gap-x-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Design Start Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Design End Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-x-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Expected Revenue
                  </label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                    placeholder="Enter expected revenue"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Expected Margin
                  </label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                    placeholder="Enter expected margin"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Section 3 - Construction Section */}
          <div className="border border-border-gray1 rounded-xl">
            <h5 className="px-6 py-4 text-neutrals-G800 font-semibold border-b border-[#E6E6E6]">
              Construction Section
            </h5>
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-2 gap-x-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Construction Start Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Construction End Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-x-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Contractor
                  </label>
                  <select className="w-full px-3 py-2 border border-border-gray1 rounded-lg">
                    <option>Select Contractor</option>
                    <option>Contractor A</option>
                    <option>Contractor B</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Budget
                  </label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                    placeholder="Enter budget"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Section 4 - Client Details */}
          <div className="border border-border-gray1 rounded-xl">
            <h5 className="px-6 py-4 text-neutrals-G800 font-semibold border-b border-[#E6E6E6]">
              Client Details
            </h5>
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-2 gap-x-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Client Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                    placeholder="Enter client name"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Client Email
                  </label>
                  <input
                    type="email"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                    placeholder="Enter client email"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-x-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    Client Phone
                  </label>
                  <input
                    type="tel"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                    placeholder="Enter client phone"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutrals-G800">
                    WhatsApp Number
                  </label>
                  <input
                    type="tel"
                    className="w-full px-3 py-2 border border-border-gray1 rounded-lg"
                    placeholder="Enter WhatsApp number"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Section 5 - Critical Actions */}
          <div className="border border-red-200 rounded-xl bg-red-50">
            <h5 className="px-6 py-4 text-red-800 font-semibold border-b border-red-200">
              Critical Actions
            </h5>
            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-red-200">
                <div>
                  <h6 className="font-medium text-red-800">
                    Delete this project permanently
                  </h6>
                  <p className="text-sm text-red-600">
                    This action cannot be undone.
                  </p>
                </div>
                <Button
                  variant="destructive"
                  className="bg-red-600 hover:bg-red-700"
                >
                  Delete Project
                </Button>
              </div>
            </div>
          </div>

          {/* Extra content to ensure scrolling */}
          <div className="border border-border-gray1 rounded-xl">
            <h5 className="px-6 py-4 text-neutrals-G800 font-semibold border-b border-[#E6E6E6]">
              Extra Section for Testing
            </h5>
            <div className="p-6">
              <p className="text-neutrals-G600 mb-4">
                This is extra content to ensure we have enough height to test
                scrolling behavior. The page should scroll smoothly without any
                white space issues.
              </p>
              <div className="space-y-4">
                {Array.from({ length: 10 }, (_, i) => (
                  <div key={i} className="p-4 bg-neutrals-G10 rounded-lg">
                    <h6 className="font-medium text-neutrals-G800">
                      Test Item {i + 1}
                    </h6>
                    <p className="text-sm text-neutrals-G600">
                      This is test content item {i + 1} to add more height to
                      the page. We want to ensure that scrolling works properly
                      and there&apos;s no white space at the bottom.
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TestScrollPage;
