"use client";

import { format } from "date-fns";
import Image from "next/image";

import useGetProjectById from "@/services/project/getProject";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import ProjectStatusComponent from "./ProjectStatusComponent";

type ProjectHeaderInfoProps = {
  projectId: string;
};

const ProjectHeaderInfo: React.FC<ProjectHeaderInfoProps> = ({ projectId }) => {
  const { data: projectData, isLoading, error } = useGetProjectById(projectId);

  if (isLoading) {
    return (
      <div className="flex items-center mx-auto ">
        <Loader />
      </div>
    );
  }

  if (error || !projectData) {
    return <ErrorText entity="project" />;
  }

  const formatDateRange = (startDate?: string, endDate?: string) => {
    if (!startDate || !endDate) return "No timeline set";

    try {
      const start = format(new Date(startDate), "dd MMM yyyy");
      const end = format(new Date(endDate), "dd MMM yyyy");
      return `${start} - ${end}`;
    } catch {
      return "Invalid dates";
    }
  };

  const getProjectTimeline = () => {
    // Check if we have design or construction specific dates
    const designStartDate = (projectData as any).designStartDate;
    const designEndDate = (projectData as any).designEndDate;
    const contractorStartDate = (projectData as any).contractorStartDate;
    const contractorEndDate = (projectData as any).contractorEndDate;

    if (
      designStartDate &&
      designEndDate &&
      contractorStartDate &&
      contractorEndDate
    ) {
      // Both design and construction
      const designStart = format(new Date(designStartDate), "dd MMM yyyy");
      const constructionEnd = format(
        new Date(contractorEndDate),
        "dd MMM yyyy",
      );
      return `${designStart} - ${constructionEnd}`;
    } else if (designStartDate && designEndDate) {
      // Design only
      return formatDateRange(designStartDate, designEndDate);
    } else if (contractorStartDate && contractorEndDate) {
      // Construction only
      return formatDateRange(contractorStartDate, contractorEndDate);
    } else {
      // Fallback to project dates
      return formatDateRange(projectData.startDate, projectData.endDate);
    }
  };

  const projectType = (projectData as any).projectType || "Not specified";
  const numberOfFloors = (projectData as any).numberOfFloors || "Not specified";
  const timeline = getProjectTimeline();

  return (
    <div className="flex justify-between items-center w-full">
      {/* Left section - Project Info */}
      <div>
        <h1 className="text-neutrals-G900 font-semibold text-lg">
          {projectData.name}
        </h1>
        <div className="flex items-center gap-2 text-xs text-neutrals-G300 ">
          <span>{projectType}</span>
          <span className="text-[#BFD8F9] text-lg mb-1">•</span>
          <span>{timeline}</span>
          <span className="text-[#BFD8F9] text-lg mb-1">•</span>
          <span>{numberOfFloors} floors</span>
        </div>
      </div>

      {/* Right section - Status Component */}
      <ProjectStatusComponent projectData={projectData} />
    </div>
  );
};

export default ProjectHeaderInfo;
