import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import api from "@/lib/api-client";

const addItemsToTeamVault = async (body: {
  name: string;
  parentId: string;
  s3: {
    url: string;
    mimeType: string;
    size: number;
  };
}) => {
  const { data } = await api({
    url: `/vault/file`,
    method: "POST",
    data: body,
  });
  return data;
};

const useAddItemsToTeamVault = (onSuccess?: () => void) => {
  return useMutation({
    mutationFn: addItemsToTeamVault,
    onSuccess: () => {
      onSuccess?.();
    },
    onError: () => {
      toast.error("Failed to add items to team vault");
    },
  });
};

export default useAddItemsToTeamVault;
