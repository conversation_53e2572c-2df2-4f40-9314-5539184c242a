"use client";

import Link from "next/link";
import { useParams } from "next/navigation";

import ProjectSidebar from "@/components/dashboard/ProjectSidebar";
import ProtectedRoutes from "@/components/ProtectedRoutes";
import Sidebar from "@/components/dashboard/Sidebar";
import PageHeader from "@/components/layout/pageHeader/PageHeader";
import Arrow<PERSON>eft from "@/components/icons/ArrowLeft";
import ProjectHeaderInfo from "@/components/dashboard/ProjectHeaderInfo";

export default function ProjectLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const params = useParams();
  const projectId = params.id as string;

  return (
    <div className="flex h-screen">
      <Sidebar shrink={true} />
      <div className="flex flex-col gap-y-3 flex-1">
        <PageHeader className="p-3.5 gap-3 justify-between items-center shadow-lg">
          <div className="flex gap-3 items-start w-full">
            <Link href="/projects" className="mt-1">
              <ArrowLeft className="mt-1" />
            </Link>
            <ProjectHeaderInfo projectId={projectId} />
          </div>
        </PageHeader>
        <div className="flex flex-1 overflow-auto ml-3 pb-1 mr-5 gap-x-5">
          <ProjectSidebar />
          <main className="flex-1 overflow-y-auto scrollbar-hide">
            <ProtectedRoutes>{children}</ProtectedRoutes>
          </main>
        </div>
      </div>
    </div>
  );
}
