import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { useEffect, useMemo } from "react";
import { useInView } from "react-intersection-observer";

import {
  FormControl,
  FormItem,
  FormField,
  FormMessage,
  FormLabel,
} from "@/components/ui/form";
import { designTeamSchema } from "@/schema/designTeam";
import { Input } from "../ui/input";
import useGetTeamMembers from "@/services/teamMember/getTeamMembers";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { LoadingSpinner } from "../ui/loader";
import { cn } from "@/lib/utils";

type DesignTeamFormFieldProps = {
  form: UseFormReturn<z.infer<typeof designTeamSchema>>;
};

const DesignTeamFormField = ({ form }: DesignTeamFormFieldProps) => {
  const { ref, inView } = useInView();

  const { data, isPending, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useGetTeamMembers({ search: "" });

  const userId = form.watch("userId");

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  const teamMembers = useMemo(
    () => (data ? data.pages.flatMap((page) => page.data) : []),
    [data],
  );

  useEffect(() => {
    if (!userId || !teamMembers) return;

    const role = teamMembers.find((member) => member.userId === userId)?.role;
    if (role) form.setValue("role", role);
  }, [userId, teamMembers, form]);

  return (
    <>
      <FormField
        control={form.control}
        name="userId"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <>
                <FormLabel>Name</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value || ""}
                >
                  <SelectTrigger
                    loading={isPending}
                    className="data-[placeholder]:text-name-title text-name-title font-medium"
                  >
                    <SelectValue placeholder="Select">
                      {
                        teamMembers.find(
                          (member) => member.userId === field.value,
                        )?.name
                      }
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {teamMembers.length > 0 ? (
                      <>
                        {teamMembers.map((member) => (
                          <SelectItem key={member.userId} value={member.userId}>
                            {member.name}
                          </SelectItem>
                        ))}
                        <SelectItem
                          ref={ref}
                          value={"loading-spinner"}
                          disabled
                          className={cn(
                            "data-[disabled]:opacity-100 justify-center ",
                            isFetchingNextPage ? "mb-1" : "h-0",
                          )}
                        >
                          {isFetchingNextPage && (
                            <LoadingSpinner size={4} className="border-2 " />
                          )}
                        </SelectItem>
                      </>
                    ) : (
                      <SelectItem value="no-team-members" disabled>
                        No team members found
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="role"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Role</FormLabel>
            <FormControl>
              <Input disabled {...field} className="bg-neutrals-G40" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default DesignTeamFormField;
