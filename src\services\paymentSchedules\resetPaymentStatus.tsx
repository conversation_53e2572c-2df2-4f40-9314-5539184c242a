"use client";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export const useResetPaymentStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (scheduleId: string) => {
      const res = await api.put(`/payment-schedule/resetStatus/${scheduleId}`);
      return res.data;
    },
    onSuccess: (_data, scheduleId) => {
      toast.success("Payment status reset successfully");
      queryClient.invalidateQueries({
        queryKey: ["paymentSchedules"],
      });

      queryClient.invalidateQueries({
        queryKey: ["paymentSchedule", scheduleId],
      });
    },
    onError: () => {
      toast.error("Failed to reset payment status");
    },
  });
};
