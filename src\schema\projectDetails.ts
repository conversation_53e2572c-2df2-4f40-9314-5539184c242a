import { z } from "zod";

import { checkIsStartDateLowerThanOrEqualsToEndDate } from "@/lib/utils";

export const projectDetailsSchema = z
  .object({
    name: z.string().min(1, { message: "" }),
    projectType: z.enum(["Commercial", "Residential"]),
    numberOfFloors: z.string().min(1, { message: "" }),
    projectScope: z
      .object({
        design: z.boolean(),
        construction: z.boolean(),
      })
      .refine((data) => data.design || data.construction, {
        message: "At least one scope must be selected",
        path: ["design"],
      }),
    expectedRevenue: z.string().min(1, { message: "" }),
    requiredMargin: z.string().min(1, { message: "" }),

    designStartDate: z.date().optional(),
    designEndDate: z.date().optional(),
    contractorStartDate: z.date().optional(),
    contractorEndDate: z.date().optional(),
    contractorOrg: z.string().optional(),
    clientName: z.string().optional(),
    clientWhatsAppNo: z.string().optional(),
    location: z.string().optional(),
    status: z.enum(["ongoing", "completed", "halted"]).optional(),
    startDate: z.date().optional(),
    endDate: z.date().optional(),
  })
  .refine(
    (data) => {
      if (!data.startDate || !data.endDate) {
        return true;
      }
      return checkIsStartDateLowerThanOrEqualsToEndDate(
        new Date(data.startDate),
        new Date(data.endDate),
      );
    },
    {
      message: "End date cannot be lower than start date",
      path: ["endDate"],
    },
  )
  .refine(
    (data) => {
      if (!data.designStartDate || !data.designEndDate) {
        return true;
      }
      return checkIsStartDateLowerThanOrEqualsToEndDate(
        new Date(data.designStartDate),
        new Date(data.designEndDate),
      );
    },
    {
      message: "Design end date cannot be lower than design start date",
      path: ["designEndDate"],
    },
  )
  .refine(
    (data) => {
      if (!data.contractorStartDate || !data.contractorEndDate) {
        return true;
      }
      return checkIsStartDateLowerThanOrEqualsToEndDate(
        new Date(data.contractorStartDate),
        new Date(data.contractorEndDate),
      );
    },
    {
      message:
        "Construction end date cannot be lower than construction start date",
      path: ["contractorEndDate"],
    },
  )
  .refine(
    (data) => {
      if (!data.designStartDate || !data.startDate || !data.endDate) {
        return true;
      }
      const projectStart = new Date(data.startDate);
      const projectEnd = new Date(data.endDate);
      const designStart = new Date(data.designStartDate);
      return designStart >= projectStart && designStart <= projectEnd;
    },
    {
      message: "Design start date must be within project timeline",
      path: ["designStartDate"],
    },
  )
  .refine(
    (data) => {
      if (!data.designEndDate || !data.startDate || !data.endDate) {
        return true;
      }
      const projectStart = new Date(data.startDate);
      const projectEnd = new Date(data.endDate);
      const designEnd = new Date(data.designEndDate);
      return designEnd >= projectStart && designEnd <= projectEnd;
    },
    {
      message: "Design end date must be within project timeline",
      path: ["designEndDate"],
    },
  )
  .refine(
    (data) => {
      if (!data.contractorStartDate || !data.startDate || !data.endDate) {
        return true;
      }
      const projectStart = new Date(data.startDate);
      const projectEnd = new Date(data.endDate);
      const contractorStart = new Date(data.contractorStartDate);
      return contractorStart >= projectStart && contractorStart <= projectEnd;
    },
    {
      message: "Construction start date must be within project timeline",
      path: ["contractorStartDate"],
    },
  )
  .refine(
    (data) => {
      if (!data.contractorEndDate || !data.startDate || !data.endDate) {
        return true;
      }
      const projectStart = new Date(data.startDate);
      const projectEnd = new Date(data.endDate);
      const contractorEnd = new Date(data.contractorEndDate);
      return contractorEnd >= projectStart && contractorEnd <= projectEnd;
    },
    {
      message: "Construction end date must be within project timeline",
      path: ["contractorEndDate"],
    },
  );
