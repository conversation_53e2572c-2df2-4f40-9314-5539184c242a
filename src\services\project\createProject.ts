import { z } from "zod";

import api from "@/lib/api-client";
import { ProjectData } from "@/types/Project";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { projectSchema } from "@/schema/project";

type CreateProject = Omit<
  z.infer<typeof projectSchema>,
  | "startDate"
  | "designStartDate"
  | "contractorStartDate"
  | "endDate"
  | "designEndDate"
  | "contractorEndDate"
> & {
  startDate?: string;
  endDate?: string;
  designStartDate?: string;
  designEndDate?: string;
  contractorStartDate?: string;
  duration?: number;
  designDuration?: number;
  contractorDuration?: number;

};

const createProject = async (payload: CreateProject) => {
  try {
    const response = await api.post<ProjectData>("/projects", payload);
    return response.data;
  } catch (error) {
    console.error("Error creating project:", error);
    throw error;
  }
};

const useCreateProject = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projects"] });
    },
  });
};

export default useCreateProject;
