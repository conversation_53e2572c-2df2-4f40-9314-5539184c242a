"use client";
import DiscussionSection from "@/components/discussions/DiscussionSection";
import PageHeader from "@/components/layout/pageHeader/PageHeader";
import withTeamGuard from "@/components/TeamGuard";

const MeetingsPage = () => {
  return (
    <div className="flex  h-full flex-col gap-y-6">
      <div className="flex flex-col ">
        <PageHeader>
          <div>
            <PageHeader.Heading>Organisation Chat</PageHeader.Heading>
            <PageHeader.Description>
              Collaborate across teams, share updates, and stay aligned on
              everything beyond individual projects.
            </PageHeader.Description>
          </div>
        </PageHeader>
      </div>
      <div className="flex-1 mb-4 px-5 overflow-auto scrollbar-hide">
        <DiscussionSection />
      </div>
    </div>
  );
};

export default withTeamGuard(MeetingsPage);
