"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

type CompletionDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  completionType: "design" | "construction" | "both" | null;
  onConfirm: () => void;
  hasContractorRating?: boolean;
};

const CompletionDialog: React.FC<CompletionDialogProps> = ({
  open,
  onOpenChange,
  completionType,
  onConfirm,
  hasContractorRating = false,
}) => {
  const getDialogContent = () => {
    switch (completionType) {
      case "design":
        return {
          title: "Mark Design as Complete?",
          description:
            "Once marked as complete, this action cannot be undone. Are you sure you want to proceed?",
          confirmText: "Yes, mark as complete",
        };
      case "construction":
        return {
          title: "Mark Construction as Complete?",
          description:
            "Once marked as complete, this action cannot be undone. Are you sure you want to proceed?",
          confirmText: "Yes, mark as complete",
        };
      case "both":
        return {
          title: "Mark Design and Construction as Complete?",
          description:
            "Once marked as complete, this action cannot be undone. Are you sure you want to proceed?",
          confirmText: "Yes, mark both as complete",
        };
      default:
        return {
          title: "Mark as Complete?",
          description:
            "Once marked as complete, this action cannot be undone. Are you sure you want to proceed?",
          confirmText: "Yes, mark as complete",
        };
    }
  };

  const content = getDialogContent();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
        <DialogHeader>
          <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
            {content.title}
          </DialogTitle>
        </DialogHeader>
        <p className="text-[#474747]">{content.description}</p>
        <DialogFooter className="mt-4">
          <Button
            className="font-[600] text-[#6B6B6B] bg-[#E2E2E2] px-[16px] rounded-[8px]"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button className="rounded-[8px] px-[16px]" onClick={onConfirm}>
            {content.confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CompletionDialog;
