"use client";
import { useState } from "react";
import DynamicItems from "./DynamicItems";
import ArrowLeft from "../icons/ArrowLeft";
import Loader from "../ui/loader";
import NoDataToShow from "../ui/noDataToShow";
import useGetItemsFromTeamVault from "@/services/teamVault/getItems";

function ContentSection({
  selectedFolder,
  handleSelectedFolder,
  refetchItems,
}: {
  refetchItems: boolean;
  selectedFolder: string;
  handleSelectedFolder: (folderId: string) => void;
}) {
  const {
    data: items,
    isPending: loading,
    isSuccess,
    refetch,
  } = useGetItemsFromTeamVault(selectedFolder, [
    "teamVault",
    selectedFolder,
    refetchItems.toString(),
  ]);
  const [path, setPath] = useState<string[]>(["root"]);
  const handlePathChange = (newPath: string) => {
    handleSelectedFolder(newPath);
    if (!path.includes(newPath)) {
      setPath((prevPath) => [...prevPath, newPath]);
    }
  };

  const handleGoBack = () => {
    if (path.length === 1) return; // Can't go back from root
    const currentFolder = path[path.length - 1];
    const previousFolder = path[path.length - 2];
    handleSelectedFolder(previousFolder as string);
    setPath((prevPath) => prevPath.filter((p) => p !== currentFolder));
  };

  return (
    <div className="relative">
      <div
        onClick={handleGoBack}
        className="flex items-center gap-x-2 mb-6 text-sm cursor-pointer"
      >
        {path.length > 1 ? (
          <>
            <ArrowLeft />
            <span>Previous folder</span>
          </>
        ) : null}
      </div>
      {loading && (
        <div className="absolute inset-0 bg-white/50 backdrop-blur-sm flex items-center justify-center z-20 rounded-md">
          <Loader className="text-white" size={5} />
        </div>
      )}
      <div className="grid grid-cols-5 gap-6 gap-y-10">
        {isSuccess && (
          <>
            {items?.folders?.length === 0 && items?.files?.length === 0 ? (
              <div className="col-span-5 flex justify-center items-center min-h-[300px]">
                <NoDataToShow>
                  No files or folders found in this location
                </NoDataToShow>
              </div>
            ) : (
              <>
                {items?.folders.map((item) => (
                  <DynamicItems
                    handlePathChange={handlePathChange}
                    key={item._id}
                    item={item}
                    refetchContent={refetch}
                  />
                ))}
                {items?.files.map((item) => (
                  <DynamicItems
                    handlePathChange={handlePathChange}
                    key={item._id}
                    item={item}
                    refetchContent={refetch}
                  />
                ))}
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default ContentSection;
