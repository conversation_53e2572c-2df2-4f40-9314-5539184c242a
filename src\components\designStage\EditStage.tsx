"use client";

import { useEffect, useState, useRef } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CirclePlus } from "lucide-react";
import { format, formatDate } from "date-fns";
import { toast } from "sonner";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  designStageSchema,
  createDesignStageSchemaWithProjectValidation,
} from "@/schema/designStage";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import CalendarIcon from "@/components/icons/Calendar";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import MemberFieldsListItem from "./MemberFieldsListItem";
import Duration from "../milestones/formFields/Duration";
import Settings from "../icons/Settings";
import useEditDesignStage from "@/services/designStage/editDesignStage";
import { isValid as isValidDateFns } from "date-fns";
import { useDeleteDesignStage } from "@/services/designStage/deleteStage";
import { useSheetFormState } from "@/hooks/useSheetFormState";
import useGetProjectById from "@/services/project/getProject";

type EditStageProps = {
  projectId: string;
  stage: z.infer<typeof designStageSchema>;
};
const EditStage = ({ projectId, stage }: EditStageProps) => {
  const [open, setOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  const parseDateSafe = (dateStr?: string) => {
    if (!dateStr) return undefined;
    const parsed = new Date(dateStr);
    return isValidDateFns(parsed) ? parsed : undefined;
  };

  const popoverCloseRef = useRef<HTMLButtonElement>(null);

  // Fetch project data to get timeline for validation
  const { data: projectData } = useGetProjectById(projectId);

  // Create schema with project timeline validation
  const schemaWithProjectValidation =
    createDesignStageSchemaWithProjectValidation(
      projectData?.startDate ? new Date(projectData.startDate) : undefined,
      projectData?.endDate ? new Date(projectData.endDate) : undefined,
    );

  const form = useForm<z.infer<typeof designStageSchema>>({
    resolver: zodResolver(schemaWithProjectValidation),
    defaultValues: {
      ...stage,
      startDate: parseDateSafe(stage.startDate as any),
      endDate: parseDateSafe(stage.endDate as any),
      // endDate: isValidDateValue(stage.endDate)
      //   ? new Date(stage.endDate)
      //   : undefined,
      // startDate: new Date(stage.startDate),
      // endDate: new Date(stage.endDate),
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "members",
  });

  const { mutate: deleteStage, isPending: isPendingDeleteStage } =
    useDeleteDesignStage();

  const { mutate, isPending } = useEditDesignStage(() => {
    setOpen(false);
    toast.success("Design stage updated successfully!");
    form.reset();
  });

  const onSubmit = (values: z.infer<typeof designStageSchema>) => {
    console.log(values, "member values");

    // Filter out empty members (members without memberId)
    const validMembers =
      values.members?.filter(
        (member) => member.memberId && member.memberId.trim() !== "",
      ) || [];

    mutate({
      stageName: values.stageName,
      budgetAllocated: values.budgetAllocated,
      startDate: values.startDate
        ? formatDate(values.startDate, "yyyy-MM-dd")
        : "",

      duration: values.duration,
      members: validMembers,
      projectId,
      stageId: stage._id,
    });
  };

  const { handleOpenChange, handleExplicitClose } = useSheetFormState({
    form,
    setOpen,
    preserveOnImplicitClose: true, // For edit forms, preserve data on implicit close
  });

  useEffect(() => {
    if (open) {
      form.reset({
        ...stage,
        startDate: parseDateSafe(stage.startDate as any),
        endDate: parseDateSafe(stage.endDate as any),
        members: stage.members.length > 0 ? stage.members : [],
        // startDate: isValidDateValue(stage.startDate)
        //   ? new Date(stage.startDate)
        //   : undefined,
        // endDate: isValidDateValue(stage.endDate)
        //   ? new Date(stage.endDate)
        //   : undefined,
      });

      if (stage.members.length === 0) {
        append({
          memberId: "",
          name: "",
          role: "",
          hours: 0,
        });
      }
    }
  }, [open, stage, form]);

  const handleAddMember = () => {
    append({
      memberId: "",
      name: "",
      role: "",
      hours: 0,
    });
  };

  // const handleRemoveMember = () => {
  //   remove(fields.length - 1);
  // };

  const handleRemoveMember = (index: number) => {
    remove(index);
  };

  return (
    <>
      <Sheet open={open} onOpenChange={handleOpenChange}>
        <SheetTrigger asChild>
          <div className="flex gap-x-1 items-center cursor-pointer">
            <Settings className="fill-none stroke-neutrals-G600 size-4" />
            Stage Settings
          </div>
        </SheetTrigger>
        <SheetContent
          className="max-w-[507px] flex flex-col gap-0"
          onExplicitClose={handleExplicitClose}
        >
          <SheetHeader>
            <SheetTitle>Edit stage</SheetTitle>
          </SheetHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="flex flex-col justify-between flex-1"
            >
              <div className="space-y-4">
                <div className="flex gap-x-3">
                  <FormField
                    control={form.control}
                    name="stageName"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>Schedule name</FormLabel>
                        <FormControl>
                          <Input placeholder="Rough Design" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="budgetAllocated"
                    render={({ field }) => (
                      <FormItem className="basis-[128px]">
                        <FormLabel>Budget allocated</FormLabel>

                        <div className="relative ">
                          <FormControl>
                            <Input className="pr-[46px]" {...field} />
                          </FormControl>
                          <span className="absolute top-1/2 right-3 -translate-y-1/2 text-neutrals-G100 font-medium">
                            %
                          </span>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex gap-x-3">
                  <FormField
                    control={form.control}
                    name="startDate"
                    render={({ field }) => (
                      <FormItem className="basis-1/2">
                        <FormLabel>Start Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="input"
                                className={cn(
                                  "text-left",
                                  !field.value && "text-neutrals-G100",
                                )}
                              >
                                {/* {field.value ? (
                                format(field.value, "dd/MM/yyyy")
                              ) : (
                                <span>Choose date</span>
                              )} */}
                                {isValidDateFns(field.value) ? (
                                  format(field.value, "dd/MM/yyyy")
                                ) : (
                                  <span>Choose date</span>
                                )}
                                <CalendarIcon className="ml-auto" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <PopoverClose
                              ref={popoverCloseRef}
                              className="hidden"
                            />
                            <Calendar
                              selected={field.value ?? undefined}
                              onSelect={(value) => {
                                popoverCloseRef.current?.click();
                                field.onChange(value);
                              }}
                              mode="single"
                              showOutsideDays={false}
                              // startMonth={new Date(Date.now())}
                              endMonth={
                                new Date(
                                  Date.now() + 1000 * 60 * 60 * 24 * 365 * 10,
                                )
                              }
                            />
                          </PopoverContent>
                        </Popover>

                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="endDate"
                    render={({ field }) => (
                      <FormItem className="basis-1/2">
                        <FormLabel>End Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="input"
                                className={cn(
                                  "text-left",
                                  !field.value && "text-neutrals-G100",
                                )}
                              >
                                {/* {field.value ? (
                                format(field.value, "dd/MM/yyyy")
                              ) : (
                                <span>Choose date</span>
                              )} */}
                                {isValidDateFns(field.value) ? (
                                  format(field.value, "dd/MM/yyyy")
                                ) : (
                                  <span>Choose date</span>
                                )}
                                <CalendarIcon className="ml-auto" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <PopoverClose
                              ref={popoverCloseRef}
                              className="hidden"
                            />
                            <Calendar
                              selected={field.value ?? undefined}
                              onSelect={(value) => {
                                popoverCloseRef.current?.click();
                                field.onChange(value);
                              }}
                              mode="single"
                              showOutsideDays={false}
                              endMonth={
                                new Date(
                                  Date.now() + 1000 * 60 * 60 * 24 * 365 * 10,
                                )
                              }
                            />
                          </PopoverContent>
                        </Popover>

                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <Duration
                  value={form.watch("duration") || 0}
                  onChange={(val) => {
                    form.setValue("duration", val);
                  }}
                  startDate={form.watch("startDate")}
                  endDate={form.watch("endDate")}
                />
                <div className="border border-neutrals-G40 p-4 rounded-[8px] space-y-4">
                  {fields.length === 0 ? (
                    <div className="text-center py-4">
                      <p className="text-sm text-gray-500 mb-4">
                        No team members assigned
                      </p>
                      <Button
                        onClick={handleAddMember}
                        type="button"
                        variant="link"
                        className="hover:no-underline font-medium gap-x-1"
                      >
                        <CirclePlus className="size-4" />
                        Add Member
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-10">
                      {fields.map((field, index) => (
                        <div key={field.id} className="flex flex-col">
                          <MemberFieldsListItem
                            form={form}
                            index={index}
                            isFieldRemovable={fields.length > 1}
                            onRemoveClick={() => handleRemoveMember(index)}
                            projectId={projectId}
                          />
                        </div>
                      ))}
                      <Button
                        onClick={handleAddMember}
                        type="button"
                        variant="link"
                        className="hover:no-underline font-medium gap-x-1 self-end"
                      >
                        <CirclePlus className="size-4" />
                        Add Member
                      </Button>
                    </div>
                  )}
                </div>
              </div>
              <div className="w-full flex justify-between">
                {stage?.members?.length === 0 && (
                  <Button
                    type="button"
                    onClick={() => setConfirmDialogOpen(true)}
                    className="mr-auto px-4 py-3 sticky bottom-0 border border-[#CC0000] text-[#CC0000] bg-white hover:bg-[#CC0000] hover:text-white"
                  >
                    Delete stage
                  </Button>
                )}
                <Button
                  className="ml-auto px-4 py-3 sticky bottom-0"
                  type="submit"
                  loading={isPending}
                >
                  Edit Stage
                </Button>
              </div>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
          <DialogHeader>
            <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
              Delete Design Stage?
            </DialogTitle>
          </DialogHeader>
          <p className="text-[#474747] ">
            Do you wish to delete this design stage from the project?
          </p>
          <DialogFooter className="mt-4">
            <Button
              className="font-[600] text-[#6B6B6B] bg-[#E2E2E2] px-[16px] rounded-[8px]"
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className="rounded-[8px] px-[16px] bg-[#CC0000] text-white hover:bg-red-600"
              onClick={() => {
                if (stage._id) {
                  deleteStage(stage._id, {
                    onSuccess: () => {
                      toast.success("Design stage deleted successfully.");
                      setConfirmDialogOpen(false);
                      setOpen(false);
                    },
                    onError: () => {
                      toast.error("Failed to delete design stage.");
                    },
                  });
                }
              }}
              loading={isPendingDeleteStage}
              disabled={isPendingDeleteStage}
            >
              Yes, Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EditStage;
