import React from "react";
import Chat from "../chat/Chat";
import Loader from "../ui/loader";

function DiscussionSection() {
  const discussionId = "discussion-123"; // Example discussion ID, replace with actual logic to get discussion ID
  return (
    <div className="flex flex-col h-full">
      <Chat
        chatId={discussionId}
        messages={messages[discussionId]}
        className="flex-1 overflow-y-auto scrollbar-hide border border-neutrals-G40 rounded-xl bg-gradient-to-b from-white to-[#F3F8FF]"
      >
        <Chat.FloatingDate />
        {isPendingUserInfo || isPendingMsgData ? (
          <Loader />
        ) : (
          userInfo && <Chat.Messages user={userInfo.user} />
        )}

        <Chat.Input
          onFormSubmit={handleSubmit}
          className="border border-neutrals-G40 rounded-xl"
        />
      </Chat>
    </div>
  );
}

export default DiscussionSection;
